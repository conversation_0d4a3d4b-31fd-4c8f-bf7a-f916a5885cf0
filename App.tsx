import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View } from 'react-native';
import { GameBoard } from './src/components/game/GameBoard';
import { MainMenuScreen } from './src/components/screens/MainMenuScreen';
import { WorldSelectScreen } from './src/components/screens/WorldSelectScreen';
import { LevelSelectScreen } from './src/components/screens/LevelSelectScreen';
import { InstructionsScreen } from './src/components/screens/InstructionsScreen';
import { SettingsScreen } from './src/components/screens/SettingsScreen';
import { gameStateManager, GameProgress } from './src/utils/GameStateManager';

type Screen = 'menu' | 'worldSelect' | 'levelSelect' | 'game' | 'instructions' | 'settings';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<Screen>('menu');
  const [progress, setProgress] = useState<GameProgress | null>(null);
  const [selectedWorld, setSelectedWorld] = useState(1);
  const [selectedLevel, setSelectedLevel] = useState(1);

  useEffect(() => {
    loadProgress();
  }, []);

  const loadProgress = async () => {
    const gameProgress = await gameStateManager.loadProgress();
    setProgress(gameProgress);
    const { world, level } = gameStateManager.getCurrentWorldAndLevel();
    setSelectedWorld(world);
    setSelectedLevel(level);
  };

  const handleGameComplete = async (score: number) => {
    await gameStateManager.completeLevel(selectedWorld, selectedLevel, score);
    await loadProgress(); // Refresh progress
    setCurrentScreen('levelSelect'); // Return to level select
  };

  const handleWorldSelect = (world: number) => {
    setSelectedWorld(world);
    setCurrentScreen('levelSelect');
  };

  const handleLevelSelect = async (level: number) => {
    setSelectedLevel(level);
    await gameStateManager.setCurrentWorldAndLevel(selectedWorld, level);
    setCurrentScreen('game');
  };

  const renderScreen = () => {
    switch (currentScreen) {
      case 'menu':
        return (
          <MainMenuScreen
            onStartGame={() => setCurrentScreen('game')}
            onShowInstructions={() => setCurrentScreen('instructions')}
            onShowWorldSelect={() => setCurrentScreen('worldSelect')}
            onShowLevelSelect={() => setCurrentScreen('levelSelect')}
            onShowSettings={() => setCurrentScreen('settings')}
          />
        );

      case 'worldSelect':
        return (
          <WorldSelectScreen
            unlockedWorlds={progress?.unlockedWorlds || [1]}
            completedWorlds={progress?.completedWorlds || []}
            onWorldSelect={handleWorldSelect}
            onBackPress={() => setCurrentScreen('menu')}
          />
        );

      case 'levelSelect':
        return (
          <LevelSelectScreen
            currentWorld={selectedWorld}
            unlockedLevels={progress?.unlockedLevels[selectedWorld] || [1]}
            completedLevels={progress?.completedLevels[selectedWorld] || []}
            onLevelSelect={handleLevelSelect}
            onBackPress={() => setCurrentScreen('worldSelect')}
          />
        );

      case 'game':
        return (
          <GameBoard
            currentWorld={selectedWorld}
            currentLevel={selectedLevel}
            onGameComplete={handleGameComplete}
            onBackPress={() => setCurrentScreen('levelSelect')}
          />
        );

      case 'instructions':
        return (
          <InstructionsScreen
            onBackPress={() => setCurrentScreen('menu')}
          />
        );

      case 'settings':
        return (
          <SettingsScreen
            onBackPress={() => setCurrentScreen('menu')}
          />
        );

      default:
        return (
          <MainMenuScreen
            onStartGame={() => setCurrentScreen('game')}
            onShowInstructions={() => setCurrentScreen('instructions')}
            onShowWorldSelect={() => setCurrentScreen('worldSelect')}
            onShowLevelSelect={() => setCurrentScreen('levelSelect')}
            onShowSettings={() => setCurrentScreen('settings')}
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      {renderScreen()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#87CEEB',
  },
});
