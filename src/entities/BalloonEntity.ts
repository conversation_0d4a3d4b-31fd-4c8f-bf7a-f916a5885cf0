import { BalloonEntity, BalloonType, Position, Velocity, BALLOON_TYPES, GAME_CONFIG } from '../types/GameTypes';

export class Balloon {
  public id: string;
  public type: BalloonType;
  public position: Position;
  public velocity: Velocity;
  public isPopped: boolean;
  public createdAt: number;
  public radius: number;
  public size: number; // Actual randomized size
  public mass: number; // Actual randomized mass

  constructor(
    x: number,
    y: number,
    balloonType?: BalloonType,
    direction?: 'top-left' | 'top' | 'top-right',
    angle?: number
  ) {
    this.id = `balloon_${Date.now()}_${Math.random()}`;
    this.type = balloonType || this.getRandomBalloonType();
    this.position = { x, y };

    // Randomize size (75% variation: 0.625x to 1.375x of base size)
    const sizeVariation = 0.625 + Math.random() * 0.75; // 0.625 to 1.375
    this.size = this.type.baseSize * sizeVariation;
    this.radius = this.size / 2;

    // Randomize mass (75% variation: 0.5x to 1.75x)
    // Larger balloons tend to be faster (higher mass = higher speed)
    const massVariation = 0.5 + Math.random() * 1.25; // 0.5 to 1.75
    this.mass = massVariation;

    // Calculate velocity based on direction and angle
    if (direction && angle !== undefined) {
      // Calculate velocity components from angle
      const speed = GAME_CONFIG.BASE_BALLOON_FLOAT_SPEED * this.mass;
      this.velocity = {
        x: Math.cos(angle) * speed,
        y: -Math.sin(angle) * speed // Negative because y increases downward
      };
    } else {
      // Default behavior: float straight up
      this.velocity = { x: 0, y: -GAME_CONFIG.BASE_BALLOON_FLOAT_SPEED * this.mass };
    }

    this.isPopped = false;
    this.createdAt = Date.now();
  }

  private getRandomBalloonType(): BalloonType {
    const random = Math.random();

    // 10% chance for special balloons
    if (random < 0.05) {
      return BALLOON_TYPES.GOLD;
    } else if (random < 0.1) {
      return BALLOON_TYPES.SILVER;
    }

    // Regular balloons
    const regularTypes = [
      BALLOON_TYPES.RED,
      BALLOON_TYPES.BLUE,
      BALLOON_TYPES.GREEN,
      BALLOON_TYPES.YELLOW,
    ];
    return regularTypes[Math.floor(Math.random() * regularTypes.length)];
  }

  public update(deltaTime: number): void {
    if (this.isPopped) return;

    // Update position based on velocity
    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;

    // Add slight horizontal wobble for realistic movement (scaled by mass)
    const wobbleIntensity = 10 / this.mass; // Lighter balloons wobble more
    const wobble = Math.sin((Date.now() - this.createdAt) * 0.002) * wobbleIntensity;
    this.position.x += wobble * deltaTime;
  }

  public pop(): void {
    this.isPopped = true;
  }

  public applySpeedMultiplier(multiplier: number): void {
    // Apply the level-based speed multiplier to the balloon's velocity
    this.velocity.y *= multiplier;
  }

  public isOffScreen(_screenHeight: number): boolean {
    return this.position.y < -this.radius;
  }

  public containsPoint(x: number, y: number): boolean {
    const distance = Math.sqrt(
      Math.pow(x - this.position.x, 2) + Math.pow(y - this.position.y, 2)
    );
    return distance <= this.radius;
  }

  public getDistanceTo(other: Balloon): number {
    return Math.sqrt(
      Math.pow(this.position.x - other.position.x, 2) +
      Math.pow(this.position.y - other.position.y, 2)
    );
  }

  public isCollidingWith(other: Balloon): boolean {
    if (this.isPopped || other.isPopped) return false;
    const distance = this.getDistanceTo(other);
    return distance < (this.radius + other.radius);
  }

  public resolveCollision(other: Balloon): void {
    if (this.isPopped || other.isPopped) return;

    // Calculate collision vector
    const dx = other.position.x - this.position.x;
    const dy = other.position.y - this.position.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance === 0) return; // Prevent division by zero

    // Normalize collision vector
    const nx = dx / distance;
    const ny = dy / distance;

    // Separate overlapping balloons
    const overlap = (this.radius + other.radius) - distance;
    const separationX = (overlap * nx) / 2;
    const separationY = (overlap * ny) / 2;

    this.position.x -= separationX;
    this.position.y -= separationY;
    other.position.x += separationX;
    other.position.y += separationY;

    // Calculate relative velocity
    const relativeVelocityX = other.velocity.x - this.velocity.x;
    const relativeVelocityY = other.velocity.y - this.velocity.y;

    // Calculate relative velocity along collision normal
    const velocityAlongNormal = relativeVelocityX * nx + relativeVelocityY * ny;

    // Don't resolve if velocities are separating
    if (velocityAlongNormal > 0) return;

    // Calculate restitution (bounciness)
    const restitution = 0.8;

    // Calculate impulse scalar
    const impulse = -(1 + restitution) * velocityAlongNormal;
    const totalMass = this.mass + other.mass;
    const impulseScalar = impulse / totalMass;

    // Apply impulse
    const impulseX = impulseScalar * nx;
    const impulseY = impulseScalar * ny;

    this.velocity.x -= impulseX * other.mass;
    this.velocity.y -= impulseY * other.mass;
    other.velocity.x += impulseX * this.mass;
    other.velocity.y += impulseY * this.mass;
  }

  public toEntity(): BalloonEntity {
    return {
      id: this.id,
      type: this.type,
      position: this.position,
      velocity: this.velocity,
      isPopped: this.isPopped,
      createdAt: this.createdAt,
    };
  }
}
