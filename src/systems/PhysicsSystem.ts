import { Balloon } from '../entities/BalloonEntity';
import { LevelConfig } from '../types/GameTypes';

export class PhysicsSystem {
  private screenWidth: number;
  private screenHeight: number;
  private windForce: number = 0;
  private windDirection: number = 1; // 1 for right, -1 for left
  private windChangeTime: number = 0;
  private readonly WIND_CHANGE_INTERVAL = 5000; // Change wind every 5 seconds
  private levelConfig?: LevelConfig;

  constructor(screenWidth: number, screenHeight: number) {
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.updateWind();
  }

  private updateWind(): void {
    // Generate random wind force and direction
    this.windForce = Math.random() * 30; // 0-30 pixels per second
    this.windDirection = Math.random() > 0.5 ? 1 : -1;
    this.windChangeTime = Date.now();
  }

  public updateBalloons(balloons: Balloon[], deltaTime: number): void {
    // Update wind periodically
    if (Date.now() - this.windChangeTime > this.WIND_CHANGE_INTERVAL) {
      this.updateWind();
    }

    // Update balloon positions and apply forces
    balloons.forEach(balloon => {
      if (!balloon.isPopped) {
        // Apply wind force (scaled by mass - lighter balloons affected more by wind)
        const windEffect = this.windForce * this.windDirection * deltaTime * 0.1 / balloon.mass;
        balloon.velocity.x += windEffect;

        balloon.update(deltaTime);
        this.applyBoundaries(balloon);
      }
    });

    // Handle balloon-to-balloon collisions
    this.handleBalloonCollisions(balloons);
  }

  private applyBoundaries(balloon: Balloon): void {
    const restitution = 0.7; // Bounce dampening factor

    // Left boundary
    if (balloon.position.x - balloon.radius < 0) {
      balloon.position.x = balloon.radius;
      balloon.velocity.x = Math.abs(balloon.velocity.x) * restitution;
    }

    // Right boundary
    if (balloon.position.x + balloon.radius > this.screenWidth) {
      balloon.position.x = this.screenWidth - balloon.radius;
      balloon.velocity.x = -Math.abs(balloon.velocity.x) * restitution;
    }

    // Top boundary - DO NOT BOUNCE, let balloons pass through for penalty system
    // (Balloons escaping through top will be handled in removeOffScreenBalloons)

    // Bottom boundary
    if (balloon.position.y + balloon.radius > this.screenHeight) {
      balloon.position.y = this.screenHeight - balloon.radius;
      balloon.velocity.y = -Math.abs(balloon.velocity.y) * restitution;
    }
  }

  private handleBalloonCollisions(balloons: Balloon[]): void {
    // Check collisions between all pairs of balloons
    for (let i = 0; i < balloons.length; i++) {
      for (let j = i + 1; j < balloons.length; j++) {
        const balloonA = balloons[i];
        const balloonB = balloons[j];

        if (balloonA.isCollidingWith(balloonB)) {
          balloonA.resolveCollision(balloonB);
        }
      }
    }
  }

  public removeOffScreenBalloons(balloons: Balloon[]): { remaining: Balloon[]; escaped: Balloon[] } {
    const remaining: Balloon[] = [];
    const escaped: Balloon[] = [];

    balloons.forEach(balloon => {
      if (balloon.isOffScreen(this.screenHeight)) {
        // Only count as escaped if balloon went off the top (negative y)
        if (balloon.position.y < -balloon.radius) {
          escaped.push(balloon);
        }
        // Balloons that go off other edges are just removed without penalty
      } else {
        remaining.push(balloon);
      }
    });

    return { remaining, escaped };
  }

  public spawnBalloon(): Balloon {
    const x = Math.random() * (this.screenWidth - 60) + 30; // 30px margin
    const y = this.screenHeight + 30; // Start below screen
    const balloon = new Balloon(x, y);

    // Apply level-specific speed multiplier if available
    if (this.levelConfig) {
      balloon.applySpeedMultiplier(this.levelConfig.balloonSpeedMultiplier);
    }

    return balloon;
  }

  public updateScreenDimensions(width: number, height: number): void {
    this.screenWidth = width;
    this.screenHeight = height;
  }

  public getWindInfo(): { force: number; direction: number } {
    return {
      force: this.windForce,
      direction: this.windDirection,
    };
  }

  public setLevelConfig(levelConfig: LevelConfig): void {
    this.levelConfig = levelConfig;
  }
}
