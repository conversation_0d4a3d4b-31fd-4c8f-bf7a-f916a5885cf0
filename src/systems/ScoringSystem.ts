import { BalloonEntity } from '../types/GameTypes';

export class ScoringSystem {
  private score: number = 0;
  private combo: number = 0;
  private lastPopTime: number = 0;
  private readonly COMBO_TIMEOUT = 2000; // 2 seconds to maintain combo

  public getScore(): number {
    return this.score;
  }

  public getCombo(): number {
    return this.combo;
  }

  public popBalloon(balloon: BalloonEntity): number {
    const currentTime = Date.now();
    const timeSinceLastPop = currentTime - this.lastPopTime;

    // Reset combo if too much time has passed
    if (timeSinceLastPop > this.COMBO_TIMEOUT) {
      this.combo = 0;
    }

    // Increment combo
    this.combo++;
    this.lastPopTime = currentTime;

    // Calculate points with combo multiplier
    const basePoints = balloon.type.points;
    const comboMultiplier = Math.min(this.combo, 10); // Cap at 10x multiplier
    const points = basePoints * comboMultiplier;

    this.score += points;
    return points;
  }

  public reset(): void {
    this.score = 0;
    this.combo = 0;
    this.lastPopTime = 0;
  }

  public getComboMultiplier(): number {
    return Math.min(this.combo, 10);
  }

  public penalizeBalloonEscape(balloon: BalloonEntity): number {
    // Deduct points when a balloon escapes to the top
    const penalty = balloon.type.points;
    this.score = Math.max(0, this.score - penalty); // Don't go below 0

    // Reset combo when balloon escapes
    this.combo = 0;

    return penalty;
  }
}
