import { TouchEvent } from '../types/GameTypes';
import { Balloon } from '../entities/BalloonEntity';

export class InputSystem {
  private touchHandlers: Array<(touch: TouchEvent) => void> = [];

  public addTouchHandler(handler: (touch: TouchEvent) => void): void {
    this.touchHandlers.push(handler);
  }

  public removeTouchHandler(handler: (touch: TouchEvent) => void): void {
    const index = this.touchHandlers.indexOf(handler);
    if (index > -1) {
      this.touchHandlers.splice(index, 1);
    }
  }

  public handleTouch(x: number, y: number): void {
    const touchEvent: TouchEvent = {
      x,
      y,
      timestamp: Date.now(),
    };

    this.touchHandlers.forEach(handler => handler(touchEvent));
  }

  public checkBalloonHit(touch: TouchEvent, balloons: Balloon[]): Balloon | null {
    // Check balloons in reverse order (top to bottom rendering)
    for (let i = balloons.length - 1; i >= 0; i--) {
      const balloon = balloons[i];
      if (!balloon.isPopped && balloon.containsPoint(touch.x, touch.y)) {
        return balloon;
      }
    }
    return null;
  }
}
