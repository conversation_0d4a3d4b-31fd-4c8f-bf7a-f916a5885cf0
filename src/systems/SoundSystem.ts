import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';

export class SoundSystem {
  private sounds: { [key: string]: Audio.Sound } = {};
  private isEnabled: boolean = true;
  private hapticsEnabled: boolean = true;

  constructor() {
    this.initializeSounds();
  }

  private async initializeSounds() {
    try {
      // Set audio mode for games
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
    } catch (error) {
      console.warn('Failed to initialize audio:', error);
    }
  }

  // Create synthetic pop sound using Web Audio API (for web) or simple beep
  private createPopSound(): Audio.Sound | null {
    // For now, we'll use haptic feedback instead of actual sounds
    // In a real implementation, you'd load actual sound files
    return null;
  }

  public async playPopSound(pitch: number = 1.0) {
    if (!this.isEnabled) return;

    try {
      // Use haptic feedback for pop sensation
      if (this.hapticsEnabled) {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      // In a real implementation, you would play an actual sound file here
      // For now, we'll just use haptics
    } catch (error) {
      console.warn('Failed to play pop sound:', error);
    }
  }

  public async playComboSound(comboLevel: number) {
    if (!this.isEnabled) return;

    try {
      if (this.hapticsEnabled) {
        // Stronger haptic for higher combos
        const intensity = comboLevel > 5 ? 
          Haptics.ImpactFeedbackStyle.Heavy : 
          Haptics.ImpactFeedbackStyle.Medium;
        
        await Haptics.impactAsync(intensity);
      }
    } catch (error) {
      console.warn('Failed to play combo sound:', error);
    }
  }

  public async playGameOverSound() {
    if (!this.isEnabled) return;

    try {
      if (this.hapticsEnabled) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      }
    } catch (error) {
      console.warn('Failed to play game over sound:', error);
    }
  }

  public async playStartSound() {
    if (!this.isEnabled) return;

    try {
      if (this.hapticsEnabled) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.warn('Failed to play start sound:', error);
    }
  }

  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  public setHapticsEnabled(enabled: boolean) {
    this.hapticsEnabled = enabled;
  }

  public isAudioEnabled(): boolean {
    return this.isEnabled;
  }

  public areHapticsEnabled(): boolean {
    return this.hapticsEnabled;
  }

  public async cleanup() {
    // Clean up any loaded sounds
    for (const sound of Object.values(this.sounds)) {
      try {
        await sound.unloadAsync();
      } catch (error) {
        console.warn('Failed to unload sound:', error);
      }
    }
    this.sounds = {};
  }
}
