import React, { useEffect, useRef, useState } from 'react';
import { 
  View, 
  StyleSheet, 
  Dimensions, 
  TouchableWithoutFeedback,
  Text,
  Pressable
} from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface SimpleBalloon {
  id: string;
  x: number;
  y: number;
  color: string;
}

export const SimpleGameBoard: React.FC = () => {
  const [balloons, setBalloons] = useState<SimpleBalloon[]>([]);
  const [score, setScore] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A'];

  const spawnBalloon = () => {
    const newBalloon: SimpleBalloon = {
      id: `balloon_${Date.now()}`,
      x: Math.random() * (SCREEN_WIDTH - 60) + 30,
      y: SCREEN_HEIGHT + 30,
      color: colors[Math.floor(Math.random() * colors.length)],
    };
    setBalloons(prev => [...prev, newBalloon]);
  };

  const startGame = () => {
    console.log('Starting simple game');
    setIsPlaying(true);
    setScore(0);
    setBalloons([]);
    
    // Spawn balloons every 2 seconds
    intervalRef.current = setInterval(() => {
      spawnBalloon();
    }, 2000);

    // Stop game after 30 seconds
    setTimeout(() => {
      stopGame();
    }, 30000);
  };

  const stopGame = () => {
    console.log('Stopping simple game');
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const handleTouch = (event: any) => {
    if (!isPlaying) return;

    const { locationX, locationY } = event.nativeEvent || {};
    const x = locationX ?? event.nativeEvent?.pageX ?? 0;
    const y = locationY ?? event.nativeEvent?.pageY ?? 0;

    console.log('Touch at:', x, y);

    // Check if we hit a balloon
    setBalloons(prev => {
      const hitBalloonIndex = prev.findIndex(balloon => {
        const distance = Math.sqrt(
          Math.pow(x - balloon.x, 2) + Math.pow(y - balloon.y, 2)
        );
        return distance <= 30; // 30px radius
      });

      if (hitBalloonIndex >= 0) {
        console.log('Hit balloon!');
        setScore(s => s + 10);
        return prev.filter((_, index) => index !== hitBalloonIndex);
      }

      return prev;
    });
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback onPress={handleTouch}>
        <View style={styles.gameArea}>
          {/* Background */}
          <View style={styles.background} />
          
          {/* HUD */}
          <View style={styles.hud}>
            <Text style={styles.scoreText}>Score: {score}</Text>
            <Text style={styles.statusText}>
              {isPlaying ? 'Playing...' : 'Tap Start to Play'}
            </Text>
          </View>
          
          {/* Balloons */}
          {balloons.map((balloon) => (
            <View
              key={balloon.id}
              style={[
                styles.balloon,
                {
                  left: balloon.x - 30,
                  top: balloon.y - 30,
                  backgroundColor: balloon.color,
                }
              ]}
            />
          ))}
          
          {/* Start Button */}
          {!isPlaying && (
            <View style={styles.startContainer}>
              <Pressable style={styles.startButton} onPress={startGame}>
                <Text style={styles.startButtonText}>Start Simple Game</Text>
              </Pressable>
            </View>
          )}
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gameArea: {
    flex: 1,
    position: 'relative',
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#87CEEB',
  },
  hud: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    zIndex: 1000,
  },
  scoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E86AB',
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  balloon: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  startContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  startButton: {
    backgroundColor: '#2E86AB',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
