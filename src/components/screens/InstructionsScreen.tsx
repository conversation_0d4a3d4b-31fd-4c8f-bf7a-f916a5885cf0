import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { i18n } from '../../utils/i18n';

interface InstructionsScreenProps {
  onBackPress: () => void;
}

export const InstructionsScreen: React.FC<InstructionsScreenProps> = ({
  onBackPress,
}) => {
  const [translations, setTranslations] = useState(i18n.getTranslations());

  useEffect(() => {
    const handleLanguageChange = () => {
      setTranslations(i18n.getTranslations());
    };

    i18n.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18n.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#87CEEB', '#98D8E8', '#B0E0E6']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>{translations.back}</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{translations.howToPlayTitle}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Instructions */}
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.objective}</Text>
            <Text style={styles.sectionText}>
              {translations.objectiveText}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.balloonTypes}</Text>
            <View style={styles.balloonType}>
              <View style={[styles.balloonExample, { backgroundColor: '#FF6B6B' }]} />
              <Text style={styles.balloonText}>{translations.regularBalloons}</Text>
            </View>
            <View style={styles.balloonType}>
              <View style={[styles.balloonExample, { backgroundColor: '#C0C0C0' }]} />
              <Text style={styles.balloonText}>{translations.silverBalloons}</Text>
            </View>
            <View style={styles.balloonType}>
              <View style={[styles.balloonExample, { backgroundColor: '#FFD700' }]} />
              <Text style={styles.balloonText}>{translations.goldBalloons}</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.controls}</Text>
            <Text style={styles.sectionText}>
              {translations.controlsText}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.progression}</Text>
            <Text style={styles.sectionText}>
              {translations.progressionText}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.gameSpeed}</Text>
            <Text style={styles.sectionText}>
              {translations.gameSpeedText}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.tips}</Text>
            <Text style={styles.sectionText}>
              {translations.tipsText}
            </Text>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>{translations.goodLuck}</Text>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    color: '#2E86AB',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E86AB',
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 15,
    borderRadius: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E86AB',
    marginBottom: 10,
  },
  sectionText: {
    fontSize: 16,
    color: '#2E86AB',
    lineHeight: 24,
  },
  balloonType: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  balloonExample: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  balloonText: {
    fontSize: 16,
    color: '#2E86AB',
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 18,
    color: '#2E86AB',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
