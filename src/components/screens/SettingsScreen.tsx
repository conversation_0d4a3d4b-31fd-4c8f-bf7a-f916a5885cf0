import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { i18n, Language } from '../../utils/i18n';
import { gameStateManager } from '../../utils/GameStateManager';

interface SettingsScreenProps {
  onBackPress: () => void;
}

export const SettingsScreen: React.FC<SettingsScreenProps> = ({
  onBackPress,
}) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [translations, setTranslations] = useState(i18n.getTranslations());

  useEffect(() => {
    loadLanguage();
    
    const handleLanguageChange = (language: Language) => {
      setCurrentLanguage(language);
      setTranslations(i18n.getTranslations());
    };

    i18n.addLanguageChangeListener(handleLanguageChange);
    
    return () => {
      i18n.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  const loadLanguage = async () => {
    const language = await i18n.loadLanguage();
    setCurrentLanguage(language);
    setTranslations(i18n.getTranslations());
  };

  const handleLanguageChange = async (language: Language) => {
    await i18n.setLanguage(language);
  };

  const handleResetProgress = () => {
    Alert.alert(
      translations.resetProgress,
      translations.resetProgressWarning,
      [
        {
          text: translations.cancel,
          style: 'cancel',
        },
        {
          text: translations.confirm,
          style: 'destructive',
          onPress: async () => {
            await gameStateManager.resetProgress();
            Alert.alert(
              translations.resetProgress,
              translations.resetComplete,
              [{ text: 'OK' }]
            );
          },
        },
      ]
    );
  };

  const renderLanguageOption = (language: Language, label: string) => (
    <TouchableOpacity
      key={language}
      style={[
        styles.languageOption,
        currentLanguage === language && styles.selectedLanguageOption,
      ]}
      onPress={() => handleLanguageChange(language)}
    >
      <View style={styles.languageContent}>
        <Text style={[
          styles.languageLabel,
          currentLanguage === language && styles.selectedLanguageLabel,
        ]}>
          {label}
        </Text>
        {currentLanguage === language && (
          <Text style={styles.checkmark}>✓</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderStatistics = () => {
    const stats = gameStateManager.getStatistics();
    
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{translations.statistics}</Text>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>{translations.totalScore}:</Text>
          <Text style={styles.statValue}>{stats.totalScore.toLocaleString()}</Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>{translations.gamesPlayed}:</Text>
          <Text style={styles.statValue}>{stats.gamesPlayed}</Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>{translations.averageScore}:</Text>
          <Text style={styles.statValue}>{Math.round(stats.averageScore).toLocaleString()}</Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>{translations.totalLevelsCompleted}:</Text>
          <Text style={styles.statValue}>{stats.totalLevelsCompleted}/{stats.totalLevelsAvailable}</Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>{translations.completionPercentage}:</Text>
          <Text style={styles.statValue}>{stats.completionPercentage.toFixed(1)}%</Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>{translations.back}</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{translations.settingsTitle}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Settings Content */}
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
        >
          {/* Language Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.language}</Text>
            <Text style={styles.sectionDescription}>{translations.languageDescription}</Text>
            
            <View style={styles.languageContainer}>
              {renderLanguageOption('en', translations.english)}
              {renderLanguageOption('bs', translations.bosnian)}
            </View>
          </View>

          {/* Statistics Section */}
          {renderStatistics()}

          {/* Reset Progress Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{translations.resetProgress}</Text>
            <Text style={styles.sectionDescription}>{translations.resetProgressDescription}</Text>
            
            <TouchableOpacity style={styles.resetButton} onPress={handleResetProgress}>
              <Text style={styles.resetButtonText}>{translations.resetProgress}</Text>
            </TouchableOpacity>
          </View>

          {/* Version */}
          <View style={styles.versionContainer}>
            <Text style={styles.versionText}>{translations.version}</Text>
          </View>
        </ScrollView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 20,
    borderRadius: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 15,
    lineHeight: 20,
  },
  languageContainer: {
    gap: 10,
  },
  languageOption: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedLanguageOption: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderColor: '#4CAF50',
  },
  languageContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  languageLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  selectedLanguageLabel: {
    color: '#4CAF50',
  },
  checkmark: {
    fontSize: 18,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  statLabel: {
    fontSize: 16,
    color: '#CCCCCC',
  },
  statValue: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  resetButton: {
    backgroundColor: '#FF5722',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  resetButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  versionContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#666666',
  },
});
