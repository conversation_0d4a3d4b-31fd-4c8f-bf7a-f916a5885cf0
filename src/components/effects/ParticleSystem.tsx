import React, { useEffect, useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

interface Particle {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  color: string;
  size: number;
  life: number;
}

interface ParticleSystemProps {
  particles: Particle[];
  onParticleComplete?: (particleId: string) => void;
}

const ParticleComponent: React.FC<{
  particle: Particle;
  onComplete?: () => void;
}> = ({ particle, onComplete }) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);
  const scale = useSharedValue(1);

  useEffect(() => {
    // Animate particle movement
    translateX.value = withTiming(particle.vx * 100, { duration: 1000 });
    translateY.value = withTiming(particle.vy * 100, { duration: 1000 });
    
    // Fade out
    opacity.value = withTiming(0, { duration: 800 });
    
    // Scale animation
    scale.value = withSpring(0.5, { damping: 10 }, () => {
      if (onComplete) {
        runOnJS(onComplete)();
      }
    });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.particle,
        {
          left: particle.x,
          top: particle.y,
          backgroundColor: particle.color,
          width: particle.size,
          height: particle.size,
        },
        animatedStyle,
      ]}
    />
  );
};

export const ParticleSystem: React.FC<ParticleSystemProps> = ({
  particles,
  onParticleComplete,
}) => {
  return (
    <View style={styles.container} pointerEvents="none">
      {particles.map((particle) => (
        <ParticleComponent
          key={particle.id}
          particle={particle}
          onComplete={() => onParticleComplete?.(particle.id)}
        />
      ))}
    </View>
  );
};

export const createPopParticles = (
  x: number,
  y: number,
  color: string,
  count: number = 8
): Particle[] => {
  const particles: Particle[] = [];
  
  for (let i = 0; i < count; i++) {
    const angle = (i / count) * Math.PI * 2;
    const speed = 50 + Math.random() * 50;
    
    particles.push({
      id: `particle_${Date.now()}_${i}`,
      x: x - 2,
      y: y - 2,
      vx: Math.cos(angle) * speed,
      vy: Math.sin(angle) * speed,
      color,
      size: 4 + Math.random() * 4,
      life: 1000,
    });
  }
  
  return particles;
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 100,
  },
  particle: {
    position: 'absolute',
    borderRadius: 10,
  },
});
