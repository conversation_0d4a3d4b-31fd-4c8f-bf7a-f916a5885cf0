import React, { useEffect } from 'react';
import { Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

interface FloatingScoreProps {
  x: number;
  y: number;
  points: number;
  combo?: number;
  onComplete?: () => void;
}

export const FloatingScore: React.FC<FloatingScoreProps> = ({
  x,
  y,
  points,
  combo = 0,
  onComplete,
}) => {
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);
  const scale = useSharedValue(0.5);

  useEffect(() => {
    // Start animation
    scale.value = withSpring(1, { damping: 10 });
    translateY.value = withTiming(-50, { duration: 1500 });
    opacity.value = withTiming(0, { duration: 1500 }, () => {
      if (onComplete) {
        runOnJS(onComplete)();
      }
    });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: translateY.value },
      { scale: scale.value },
    ],
    opacity: opacity.value,
  }));

  const getScoreText = () => {
    if (combo > 1) {
      return `+${points} x${combo}!`;
    }
    return `+${points}`;
  };

  const getScoreColor = () => {
    if (combo > 5) return '#E74C3C'; // Red for high combo
    if (combo > 2) return '#F39C12'; // Orange for medium combo
    if (points >= 50) return '#FFD700'; // Gold for special balloons
    if (points >= 25) return '#C0C0C0'; // Silver for special balloons
    return '#2ECC71'; // Green for regular balloons
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          left: x - 30, // Center the text
          top: y - 20,
        },
        animatedStyle,
      ]}
      pointerEvents="none"
    >
      <Text
        style={[
          styles.scoreText,
          {
            color: getScoreColor(),
            fontSize: combo > 3 ? 20 : 16,
            fontWeight: combo > 1 ? 'bold' : '600',
          },
        ]}
      >
        {getScoreText()}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 200,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
  },
  scoreText: {
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});
