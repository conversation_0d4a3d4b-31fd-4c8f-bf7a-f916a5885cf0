// Core game types and interfaces

export interface Position {
  x: number;
  y: number;
}

export interface Velocity {
  x: number;
  y: number;
}

export interface BalloonType {
  id: string;
  color: string;
  points: number;
  baseSize: number; // Base size - actual size will be randomized
  special?: boolean;
}

export interface BalloonEntity {
  id: string;
  type: BalloonType;
  position: Position;
  velocity: Velocity;
  isPopped: boolean;
  createdAt: number;
}

export interface GameState {
  score: number;
  level: number;
  balloons: BalloonEntity[];
  isPlaying: boolean;
  timeRemaining: number;
}

export interface TouchEvent {
  x: number;
  y: number;
  timestamp: number;
}

export interface LevelConfig {
  balloonSpeedMultiplier: number;
  balloonSpawnRate: number; // milliseconds between spawns
  world: number;
  level: number;
}

// Balloon types configuration
export const BALLOON_TYPES: { [key: string]: BalloonType } = {
  RED: {
    id: 'red',
    color: '#FF6B6B',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  BLUE: {
    id: 'blue',
    color: '#4ECDC4',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  GREEN: {
    id: 'green',
    color: '#45B7D1',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  YELLOW: {
    id: 'yellow',
    color: '#FFA07A',
    points: 10,
    baseSize: 60, // Base size - will be randomized
  },
  GOLD: {
    id: 'gold',
    color: '#FFD700',
    points: 50,
    baseSize: 60, // Base size - will be randomized
    special: true,
  },
  SILVER: {
    id: 'silver',
    color: '#C0C0C0',
    points: 25,
    baseSize: 60, // Base size - will be randomized
    special: true,
  },
};

export const GAME_CONFIG = {
  BASE_BALLOON_SPAWN_RATE: 2000, // Base spawn rate in milliseconds (level 1)
  BASE_BALLOON_FLOAT_SPEED: 200, // Base speed in pixels per second (level 1)
  GAME_DURATION: 30000, // 60 seconds
  SCREEN_WIDTH: 375, // will be updated with actual screen dimensions
  SCREEN_HEIGHT: 812, // will be updated with actual screen dimensions
  SPEED_INCREASE_PER_LEVEL: 0.2, // Speed multiplier increase per level
  SPAWN_RATE_DECREASE_PER_LEVEL: 150, // Milliseconds decrease per level (faster spawning)
};

// Level configuration calculation functions
export const calculateLevelConfig = (world: number, level: number): LevelConfig => {
  // Speed starts at 0.2 for level 1 and increases by 0.2 each level
  const baseSpeedMultiplier = 0.2;
  const balloonSpeedMultiplier = baseSpeedMultiplier + (level - 1) * GAME_CONFIG.SPEED_INCREASE_PER_LEVEL;

  // Spawn rate starts slower and gets faster each level
  // Level 1: 2000ms, Level 2: 1850ms, Level 3: 1700ms, etc.
  const balloonSpawnRate = Math.max(
    300, // Minimum spawn rate (very fast)
    GAME_CONFIG.BASE_BALLOON_SPAWN_RATE - (level - 1) * GAME_CONFIG.SPAWN_RATE_DECREASE_PER_LEVEL
  );

  return {
    balloonSpeedMultiplier,
    balloonSpawnRate,
    world,
    level,
  };
};
