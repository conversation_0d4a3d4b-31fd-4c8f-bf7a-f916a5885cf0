// Simple debug logger for tracking game issues
export class DebugLogger {
  private static isEnabled = __DEV__; // Only log in development

  static log(message: string, data?: any) {
    if (this.isEnabled) {
      console.log(`[BalloonGame] ${message}`, data || '');
    }
  }

  static warn(message: string, data?: any) {
    if (this.isEnabled) {
      console.warn(`[BalloonGame] ${message}`, data || '');
    }
  }

  static error(message: string, error?: any) {
    if (this.isEnabled) {
      console.error(`[BalloonGame] ${message}`, error || '');
    }
  }

  static gameEvent(event: string, data?: any) {
    if (this.isEnabled) {
      console.log(`[GameEvent] ${event}`, data || '');
    }
  }
}
