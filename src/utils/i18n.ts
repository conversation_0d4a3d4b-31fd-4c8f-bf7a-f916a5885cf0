import AsyncStorage from '@react-native-async-storage/async-storage';

export type Language = 'en' | 'bs';

const LANGUAGE_STORAGE_KEY = 'balloon_blast_language';

export interface Translations {
  // Main Menu
  gameTitle: string;
  gameSubtitle: string;
  continue: string;
  startGame: string;
  selectWorld: string;
  selectLevel: string;
  howToPlay: string;
  settings: string;
  
  // Progress
  currentLevel: string;
  levelsCompleted: string;
  progress: string;
  
  // World Selection
  selectWorldTitle: string;
  world: string;
  locked: string;
  completed: string;
  inProgress: string;
  completePreviousWorld: string;
  overallProgress: string;
  worldsCompleted: string;
  
  // World Names
  worldNames: {
    skyGarden: string;
    oceanDeep: string;
    forestMagic: string;
    desertStorm: string;
    spaceOdyssey: string;
  };
  
  // Level Selection
  level: string;
  
  // Game
  startLevel: string;
  fastMode: string;
  tapBalloonsToPopThem: string;
  levelComplete: string;
  gameOver: string;
  finalScore: string;
  playAgain: string;
  nextLevel: string;
  backToLevels: string;
  back: string;
  
  // Instructions
  howToPlayTitle: string;
  objective: string;
  objectiveText: string;
  balloonTypes: string;
  regularBalloons: string;
  silverBalloons: string;
  goldBalloons: string;
  controls: string;
  controlsText: string;
  progression: string;
  progressionText: string;
  gameSpeed: string;
  gameSpeedText: string;
  tips: string;
  tipsText: string;
  goodLuck: string;
  
  // Settings
  settingsTitle: string;
  language: string;
  languageDescription: string;
  english: string;
  bosnian: string;
  resetProgress: string;
  resetProgressDescription: string;
  resetProgressWarning: string;
  confirm: string;
  cancel: string;
  resetComplete: string;
  
  // Statistics
  statistics: string;
  totalScore: string;
  gamesPlayed: string;
  averageScore: string;
  totalLevelsCompleted: string;
  completionPercentage: string;
  
  // Common
  points: string;
  seconds: string;
  version: string;
}

const translations: Record<Language, Translations> = {
  en: {
    // Main Menu
    gameTitle: 'Balloon Blast',
    gameSubtitle: 'Saga',
    continue: '▶️ Continue',
    startGame: '🎮 Start Game',
    selectWorld: '🌍 Select World',
    selectLevel: '🎯 Select Level',
    howToPlay: '📖 How to Play',
    settings: '⚙️ Settings',
    
    // Progress
    currentLevel: 'Current',
    levelsCompleted: 'levels completed',
    progress: 'Progress',
    
    // World Selection
    selectWorldTitle: 'Select World',
    world: 'World',
    locked: 'Locked',
    completed: '✓ Completed',
    inProgress: 'In Progress',
    completePreviousWorld: 'Complete previous world',
    overallProgress: 'Overall Progress',
    worldsCompleted: 'worlds completed',
    
    // World Names
    worldNames: {
      skyGarden: 'Sky Garden',
      oceanDeep: 'Ocean Deep',
      forestMagic: 'Forest Magic',
      desertStorm: 'Desert Storm',
      spaceOdyssey: 'Space Odyssey',
    },
    
    // Level Selection
    level: 'Level',
    
    // Game
    startLevel: 'Start Level',
    fastMode: '⚡ Fast Mode: 3x Speed!',
    tapBalloonsToPopThem: 'Tap balloons to pop them!',
    levelComplete: 'Level Complete!',
    gameOver: 'Game Over!',
    finalScore: 'Final Score',
    playAgain: 'Play Again',
    nextLevel: 'Next Level',
    backToLevels: 'Back to Levels',
    back: '← Back',
    
    // Instructions
    howToPlayTitle: 'How to Play',
    objective: '🎯 Objective',
    objectiveText: 'Pop as many balloons as possible within 60 seconds to score points and advance through levels!',
    balloonTypes: '🎈 Balloon Types',
    regularBalloons: 'Regular Balloons - 10 points',
    silverBalloons: 'Silver Balloons - 25 points',
    goldBalloons: 'Gold Balloons - 50 points',
    controls: '🎮 Controls',
    controlsText: '• Tap anywhere on the screen to pop balloons\n• Balloons spawn from the bottom and float upward\n• Pop balloons quickly to build combos for bonus points',
    progression: '🏆 Progression',
    progressionText: '• Complete levels to unlock new ones\n• Finish all 10 levels in a world to unlock the next world\n• Each world has unique themes and challenges\n• Track your progress and high scores',
    gameSpeed: '⚡ Game Speed',
    gameSpeedText: '• Balloons spawn every 0.33 seconds (fast-paced!)\n• Balloons move 3x faster than normal\n• Quick reflexes required for high scores',
    tips: '💡 Tips',
    tipsText: '• Focus on special balloons (silver/gold) for higher scores\n• Build combos by popping balloons quickly in succession\n• Watch for balloon patterns and plan your taps\n• Don\'t let too many balloons escape off the top!',
    goodLuck: 'Good luck and have fun! 🎈',
    
    // Settings
    settingsTitle: 'Settings',
    language: 'Language',
    languageDescription: 'Choose your preferred language',
    english: 'English',
    bosnian: 'Bosanski',
    resetProgress: 'Reset Progress',
    resetProgressDescription: 'Clear all game progress and start over',
    resetProgressWarning: 'This will delete all your progress, high scores, and unlocked levels. This action cannot be undone.',
    confirm: 'Confirm',
    cancel: 'Cancel',
    resetComplete: 'Progress has been reset successfully!',
    
    // Statistics
    statistics: 'Statistics',
    totalScore: 'Total Score',
    gamesPlayed: 'Games Played',
    averageScore: 'Average Score',
    totalLevelsCompleted: 'Levels Completed',
    completionPercentage: 'Completion',
    
    // Common
    points: 'points',
    seconds: 'seconds',
    version: 'v1.0.0',
  },
  
  bs: {
    // Main Menu
    gameTitle: 'Balloon Blast',
    gameSubtitle: 'Saga',
    continue: '▶️ Nastavi',
    startGame: '🎮 Počni Igru',
    selectWorld: '🌍 Izaberi Svijet',
    selectLevel: '🎯 Izaberi Nivo',
    howToPlay: '📖 Kako Igrati',
    settings: '⚙️ Postavke',
    
    // Progress
    currentLevel: 'Trenutni',
    levelsCompleted: 'nivoa završeno',
    progress: 'Napredak',
    
    // World Selection
    selectWorldTitle: 'Izaberi Svijet',
    world: 'Svijet',
    locked: 'Zaključano',
    completed: '✓ Završeno',
    inProgress: 'U Toku',
    completePreviousWorld: 'Završi prethodni svijet',
    overallProgress: 'Ukupan Napredak',
    worldsCompleted: 'svjetova završeno',
    
    // World Names
    worldNames: {
      skyGarden: 'Nebeski Vrt',
      oceanDeep: 'Duboki Okean',
      forestMagic: 'Čarobna Šuma',
      desertStorm: 'Pustinjska Oluja',
      spaceOdyssey: 'Svemirska Odiseja',
    },
    
    // Level Selection
    level: 'Nivo',
    
    // Game
    startLevel: 'Počni Nivo',
    fastMode: '⚡ Brzi Režim: 3x Brzina!',
    tapBalloonsToPopThem: 'Dodirni balone da ih pucaš!',
    levelComplete: 'Nivo Završen!',
    gameOver: 'Kraj Igre!',
    finalScore: 'Konačni Rezultat',
    playAgain: 'Igraj Ponovo',
    nextLevel: 'Sljedeći Nivo',
    backToLevels: 'Nazad na Nivoe',
    back: '← Nazad',
    
    // Instructions
    howToPlayTitle: 'Kako Igrati',
    objective: '🎯 Cilj',
    objectiveText: 'Pucaj što više balona u roku od 60 sekundi da osvojiš bodove i napredovaš kroz nivoe!',
    balloonTypes: '🎈 Tipovi Balona',
    regularBalloons: 'Obični Baloni - 10 bodova',
    silverBalloons: 'Srebrni Baloni - 25 bodova',
    goldBalloons: 'Zlatni Baloni - 50 bodova',
    controls: '🎮 Kontrole',
    controlsText: '• Dodirni bilo gdje na ekranu da pucaš balone\n• Baloni se pojavljuju odozdo i plutaju naviše\n• Pucaj balone brzo da napraviš kombinacije za bonus bodove',
    progression: '🏆 Napredovanje',
    progressionText: '• Završi nivoe da otključaš nove\n• Završi svih 10 nivoa u svijetu da otključaš sljedeći svijet\n• Svaki svijet ima jedinstvene teme i izazove\n• Prati svoj napredak i najbolje rezultate',
    gameSpeed: '⚡ Brzina Igre',
    gameSpeedText: '• Baloni se pojavljuju svakih 0.33 sekunde (brzo!)\n• Baloni se kreću 3x brže od normalnog\n• Potrebni su brzi refleksi za visoke rezultate',
    tips: '💡 Savjeti',
    tipsText: '• Fokusiraj se na specijalne balone (srebrne/zlatne) za više bodova\n• Pravi kombinacije pucanjem balona brzo jedan za drugim\n• Pazi na obrasce balona i planiraj svoje dodire\n• Ne dozvoli da previše balona pobjegne s vrha!',
    goodLuck: 'Sretno i zabavi se! 🎈',
    
    // Settings
    settingsTitle: 'Postavke',
    language: 'Jezik',
    languageDescription: 'Izaberi željeni jezik',
    english: 'English',
    bosnian: 'Bosanski',
    resetProgress: 'Resetuj Napredak',
    resetProgressDescription: 'Obriši sav napredak u igri i počni ispočetka',
    resetProgressWarning: 'Ovo će obrisati sav tvoj napredak, najbolje rezultate i otključane nivoe. Ova akcija se ne može poništiti.',
    confirm: 'Potvrdi',
    cancel: 'Otkaži',
    resetComplete: 'Napredak je uspješno resetovan!',
    
    // Statistics
    statistics: 'Statistike',
    totalScore: 'Ukupan Rezultat',
    gamesPlayed: 'Odigrane Igre',
    averageScore: 'Prosječan Rezultat',
    totalLevelsCompleted: 'Završeni Nivoi',
    completionPercentage: 'Završenost',
    
    // Common
    points: 'bodova',
    seconds: 'sekundi',
    version: 'v1.0.0',
  },
};

class I18nManager {
  private static instance: I18nManager;
  private currentLanguage: Language = 'en';
  private listeners: Array<(language: Language) => void> = [];

  private constructor() {}

  public static getInstance(): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager();
    }
    return I18nManager.instance;
  }

  public async loadLanguage(): Promise<Language> {
    try {
      const stored = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (stored && (stored === 'en' || stored === 'bs')) {
        this.currentLanguage = stored as Language;
      }
    } catch (error) {
      console.warn('Failed to load language preference:', error);
    }
    return this.currentLanguage;
  }

  public async setLanguage(language: Language): Promise<void> {
    try {
      this.currentLanguage = language;
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
      this.notifyListeners();
    } catch (error) {
      console.warn('Failed to save language preference:', error);
    }
  }

  public getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  public getTranslations(): Translations {
    return translations[this.currentLanguage];
  }

  public t(key: keyof Translations): string {
    return translations[this.currentLanguage][key] as string;
  }

  public addLanguageChangeListener(listener: (language: Language) => void): void {
    this.listeners.push(listener);
  }

  public removeLanguageChangeListener(listener: (language: Language) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentLanguage));
  }
}

export const i18n = I18nManager.getInstance();
